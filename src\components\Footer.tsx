import { But<PERSON> } from "@/components/ui/button";
import { MessageCircle, Instagram, Youtube, Camera, Smartphone, Plane } from "lucide-react";

const Footer = () => {
  const currentYear = new Date().getFullYear();

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleWhatsAppClick = () => {
    window.open('https://wa.me/your-number', '_blank');
  };

  const services = [
    { name: "Instagram Reels", icon: <Instagram size={16} /> },
    { name: "YouTube Shorts", icon: <Youtube size={16} /> },
    { name: "Pinterest Reels", icon: <Camera size={16} /> },
    { name: "TikTok Videos", icon: <Smartphone size={16} /> },
    { name: "Drone Footage", icon: <Plane size={16} /> }
  ];

  return (
    <footer className="bg-primary text-primary-foreground">
      <div className="container mx-auto px-4 lg:px-8 py-16">
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-1">
            <div className="text-2xl font-black bg-hero-gradient bg-clip-text text-transparent mb-4 tracking-tight">
              <span className="font-extrabold text-3xl">Reel</span> for you
            </div>
            <p className="text-primary-foreground/80 mb-6 text-sm">
              Professional short video creation services for Instagram, YouTube, TikTok, Pinterest, and drone footage. 
              Starting at just $9.99 for 15 seconds.
            </p>
            <Button 
              className="bg-hero-gradient hover:opacity-90 transition-opacity"
              onClick={handleWhatsAppClick}
            >
              <MessageCircle size={18} className="mr-2" />
              Contact Us
            </Button>
          </div>

          {/* Services */}
          <div>
            <h3 className="font-semibold text-lg mb-4">Our Services</h3>
            <ul className="space-y-3">
              {services.map((service, index) => (
                <li key={index}>
                  <button 
                    onClick={() => scrollToSection('services')}
                    className="flex items-center gap-2 text-primary-foreground/80 hover:text-primary-foreground transition-colors text-sm"
                  >
                    {service.icon}
                    {service.name}
                  </button>
                </li>
              ))}
            </ul>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="font-semibold text-lg mb-4">Quick Links</h3>
            <ul className="space-y-3">
              <li>
                <button 
                  onClick={() => scrollToSection('hero')}
                  className="text-primary-foreground/80 hover:text-primary-foreground transition-colors text-sm"
                >
                  Home
                </button>
              </li>
              <li>
                <button 
                  onClick={() => scrollToSection('why-choose-us')}
                  className="text-primary-foreground/80 hover:text-primary-foreground transition-colors text-sm"
                >
                  Why Choose Us
                </button>
              </li>
              <li>
                <button 
                  onClick={() => scrollToSection('projects')}
                  className="text-primary-foreground/80 hover:text-primary-foreground transition-colors text-sm"
                >
                  Recent Projects
                </button>
              </li>
              <li>
                <button 
                  onClick={() => scrollToSection('reviews')}
                  className="text-primary-foreground/80 hover:text-primary-foreground transition-colors text-sm"
                >
                  Customer Reviews
                </button>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="font-semibold text-lg mb-4">Get In Touch</h3>
            <div className="space-y-4">
              <div>
                <p className="text-primary-foreground/80 text-sm mb-2">Ready to start your project?</p>
                <Button
                  variant="outline"
                  className="border-primary-foreground/20 text-primary-foreground hover:bg-primary-foreground hover:text-primary text-sm"
                  onClick={() => scrollToSection('services')}
                >
                  View Services
                </Button>
              </div>
              
              <div className="pt-4 border-t border-primary-foreground/10">
                <p className="text-xs text-primary-foreground/60 mb-2">What's included:</p>
                <div className="space-y-1 text-xs text-primary-foreground/80">
                  <div>✅ Professional script writing</div>
                  <div>✅ Voice-over in any language</div>
                  <div>✅ Unlimited revisions</div>
                  <div>✅ Fast delivery</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-primary-foreground/10 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="text-sm text-primary-foreground/60">
              © {currentYear} Reel for you. All rights reserved.
            </div>
            
            <div className="flex items-center gap-6 text-sm text-primary-foreground/60">
              <span>Starting at $9.99 for 15 seconds</span>
              <span>•</span>
              <span>500+ Happy Clients</span>
              <span>•</span>
              <span>5.0 ⭐ Rating</span>
            </div>
          </div>
          
          {/* Service Highlight */}
          <div className="mt-6 text-center">
            <p className="text-xs text-primary-foreground/60">
              Professional video creation services for Instagram Reels, YouTube Shorts, TikTok Videos, Pinterest Reels, and Drone Footage
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;