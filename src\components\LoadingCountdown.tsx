// Loading Countdown Component
// Reason for Component: Provide visual feedback during file upload and processing
// Task Performed: Shows countdown timer while files are being uploaded and URLs retrieved
// Linking Information: Internal - Used by OrderFormPopup component during order submission

import React, { useState, useEffect } from 'react';
import { Progress } from "@/components/ui/progress";
import { Loader2, Upload, CheckCircle } from "lucide-react";

interface LoadingCountdownProps {
  isVisible: boolean;
  onComplete: () => void;
  totalFiles: number;
  uploadedFiles: number;
  totalFileSize?: number; // Total size of all files in bytes
  isUploadComplete?: boolean; // Flag to indicate when upload is actually complete
}

const LoadingCountdown: React.FC<LoadingCountdownProps> = ({
  isVisible,
  onComplete,
  totalFiles,
  uploadedFiles,
  totalFileSize = 0,
  isUploadComplete = false
}) => {
  // Dynamic Countdown Calculation
  // Reason for Function: Calculate countdown duration based on file size
  // Task Performed: Determines appropriate countdown time based on total file size
  // Linking Information: Internal - Used to set initial countdown duration
  const calculateCountdownDuration = (fileSizeBytes: number): number => {
    const fileSizeMB = fileSizeBytes / (1024 * 1024);

    if (fileSizeMB <= 5) return 15;      // Small files: 15 seconds
    if (fileSizeMB <= 20) return 30;     // Medium files: 30 seconds
    if (fileSizeMB <= 50) return 45;     // Large files: 45 seconds
    return 60;                           // Very large files: 60 seconds
  };

  // Countdown State Management
  // Reason for State: Track countdown progress and upload status
  // Task Performed: Manages timer countdown and upload progress display
  // Linking Information: Internal - Used for countdown timer and progress tracking
  const [initialDuration] = useState(() => calculateCountdownDuration(totalFileSize));
  const [countdown, setCountdown] = useState(initialDuration);
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState<'uploading' | 'processing' | 'complete'>('uploading');

  // Upload Complete Effect
  // Reason for Effect: Handle upload completion and trigger countdown end
  // Task Performed: Ends countdown when upload is actually complete
  // Linking Information: Internal - Monitors upload completion status
  useEffect(() => {
    if (isUploadComplete && uploadedFiles === totalFiles && totalFiles > 0) {
      // Upload is complete, finish countdown immediately
      setCurrentStep('complete');
      setProgress(100);
      setTimeout(() => {
        onComplete();
      }, 1000); // Small delay to show completion
    }
  }, [isUploadComplete, uploadedFiles, totalFiles, onComplete]);

  // Countdown Effect
  // Reason for Effect: Handle countdown timer and progress updates
  // Task Performed: Updates countdown every second and calculates progress
  // Linking Information: Internal - Manages component state and calls onComplete when finished
  useEffect(() => {
    if (!isVisible) {
      setCountdown(initialDuration);
      setProgress(0);
      setCurrentStep('uploading');
      return;
    }

    const timer = setInterval(() => {
      setCountdown((prev) => {
        const newCount = prev - 1;
        const newProgress = ((initialDuration - newCount) / initialDuration) * 100;
        setProgress(newProgress);

        // Update step based on progress and upload status
        if (uploadedFiles === totalFiles && totalFiles > 0) {
          // All files uploaded, move to processing
          setCurrentStep('processing');
        } else if (newProgress < 40) {
          setCurrentStep('uploading');
        } else if (newProgress < 90) {
          setCurrentStep('processing');
        } else {
          setCurrentStep('complete');
        }

        // Only complete countdown if upload is actually done or time runs out
        if (newCount <= 0) {
          clearInterval(timer);
          if (uploadedFiles === totalFiles && totalFiles > 0) {
            // Upload complete, proceed immediately
            setTimeout(() => {
              onComplete();
            }, 500);
          } else {
            // Time ran out but upload not complete, wait a bit more
            setTimeout(() => {
              onComplete();
            }, 2000);
          }
          return 0;
        }
        return newCount;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [isVisible, onComplete, initialDuration, uploadedFiles, totalFiles]);

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-card border border-service-border rounded-2xl p-8 max-w-md w-full text-center">
        {/* Header */}
        <div className="mb-6">
          <h3 className="text-2xl font-bold text-foreground mb-2">
            Processing Your Order
          </h3>
          <p className="text-muted-foreground">
            Please wait while we upload your files and prepare your WhatsApp message
          </p>
        </div>

        {/* Progress Circle */}
        <div className="relative w-32 h-32 mx-auto mb-6">
          <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 120 120">
            {/* Background circle */}
            <circle
              cx="60"
              cy="60"
              r="50"
              stroke="currentColor"
              strokeWidth="8"
              fill="none"
              className="text-muted/20"
            />
            {/* Progress circle */}
            <circle
              cx="60"
              cy="60"
              r="50"
              stroke="currentColor"
              strokeWidth="8"
              fill="none"
              strokeDasharray={`${2 * Math.PI * 50}`}
              strokeDashoffset={`${2 * Math.PI * 50 * (1 - progress / 100)}`}
              className="text-accent transition-all duration-1000 ease-out"
              strokeLinecap="round"
            />
          </svg>
          
          {/* Center content */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              {currentStep === 'complete' ? (
                <CheckCircle className="h-8 w-8 text-green-500 mx-auto mb-1" />
              ) : (
                <Loader2 className="h-8 w-8 text-accent animate-spin mx-auto mb-1" />
              )}
              <div className="text-2xl font-bold text-foreground">{countdown}s</div>
            </div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-6">
          <Progress value={progress} className="h-2" />
          <p className="text-sm text-muted-foreground mt-2">
            {Math.round(progress)}% Complete
          </p>
        </div>

        {/* Status Steps */}
        <div className="space-y-3">
          <div className={`flex items-center gap-3 p-3 rounded-lg transition-all ${
            currentStep === 'uploading' ? 'bg-accent/10 border border-accent/20' : 
            progress > 40 ? 'bg-green-500/10 border border-green-500/20' : 'bg-muted/20'
          }`}>
            {progress > 40 ? (
              <CheckCircle className="h-5 w-5 text-green-500" />
            ) : currentStep === 'uploading' ? (
              <Upload className="h-5 w-5 text-accent animate-pulse" />
            ) : (
              <Upload className="h-5 w-5 text-muted-foreground" />
            )}
            <div className="text-left">
              <p className="font-medium text-foreground">Uploading Files</p>
              <p className="text-sm text-muted-foreground">
                {uploadedFiles} of {totalFiles} files uploaded
              </p>
            </div>
          </div>

          <div className={`flex items-center gap-3 p-3 rounded-lg transition-all ${
            currentStep === 'processing' ? 'bg-accent/10 border border-accent/20' : 
            progress > 90 ? 'bg-green-500/10 border border-green-500/20' : 'bg-muted/20'
          }`}>
            {progress > 90 ? (
              <CheckCircle className="h-5 w-5 text-green-500" />
            ) : currentStep === 'processing' ? (
              <Loader2 className="h-5 w-5 text-accent animate-spin" />
            ) : (
              <Loader2 className="h-5 w-5 text-muted-foreground" />
            )}
            <div className="text-left">
              <p className="font-medium text-foreground">Generating URLs</p>
              <p className="text-sm text-muted-foreground">
                Creating shareable links for your files
              </p>
            </div>
          </div>

          <div className={`flex items-center gap-3 p-3 rounded-lg transition-all ${
            currentStep === 'complete' ? 'bg-green-500/10 border border-green-500/20' : 'bg-muted/20'
          }`}>
            {currentStep === 'complete' ? (
              <CheckCircle className="h-5 w-5 text-green-500" />
            ) : (
              <CheckCircle className="h-5 w-5 text-muted-foreground" />
            )}
            <div className="text-left">
              <p className="font-medium text-foreground">Preparing WhatsApp</p>
              <p className="text-sm text-muted-foreground">
                Ready to send your order details
              </p>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-6 p-4 bg-secondary/30 rounded-lg">
          <p className="text-sm text-muted-foreground">
            Your files are being securely uploaded and processed. 
            WhatsApp will open automatically when ready.
          </p>
        </div>
      </div>
    </div>
  );
};

export default LoadingCountdown;
