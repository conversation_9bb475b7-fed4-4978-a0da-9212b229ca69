// Two-Step Countdown Component
// Reason for Component: Implement proper file upload process with two distinct phases
// Task Performed: Step 1 - Upload timer (increasing), Step 2 - Message generation (decreasing)
// Linking Information: Internal - Used by OrderFormPopup component for improved upload experience

import React, { useState, useEffect } from 'react';
import { Progress } from "@/components/ui/progress";
import { Loader2, Upload, CheckCircle, Clock, MessageCircle } from "lucide-react";

interface TwoStepCountdownProps {
  isVisible: boolean;
  onComplete: () => void;
  totalFiles: number;
  uploadedFiles: number;
  isUploadComplete?: boolean;
}

const TwoStepCountdown: React.FC<TwoStepCountdownProps> = ({
  isVisible,
  onComplete,
  totalFiles,
  uploadedFiles,
  isUploadComplete = false
}) => {
  // Two-Step State Management
  // Reason for State: Track which step of the process we're in
  // Task Performed: Manages step transitions and timer states
  // Linking Information: Internal - Controls UI display and timer behavior
  const [currentStep, setCurrentStep] = useState<'upload' | 'message'>('upload');
  const [uploadTimer, setUploadTimer] = useState(0); // Increasing timer for upload
  const [messageCountdown, setMessageCountdown] = useState(5); // Decreasing timer for message
  const [uploadProgress, setUploadProgress] = useState(0);

  // Step 1: Upload Timer Effect (Increasing)
  // Reason for Effect: Track upload progress with increasing timer
  // Task Performed: Increments timer until upload is complete
  // Linking Information: Internal - Monitors upload completion status
  useEffect(() => {
    if (!isVisible || currentStep !== 'upload') return;

    const timer = setInterval(() => {
      setUploadTimer(prev => prev + 1);
      
      // Calculate upload progress based on files uploaded
      if (totalFiles > 0) {
        const progress = (uploadedFiles / totalFiles) * 100;
        setUploadProgress(progress);
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [isVisible, currentStep, uploadedFiles, totalFiles]);

  // Upload Completion Effect
  // Reason for Effect: Transition to message generation step when upload completes
  // Task Performed: Switches to step 2 when all files are uploaded
  // Linking Information: Internal - Triggers step transition
  useEffect(() => {
    if (isUploadComplete && uploadedFiles === totalFiles && totalFiles > 0 && currentStep === 'upload') {
      // Upload complete, move to message generation step
      setTimeout(() => {
        setCurrentStep('message');
      }, 1000); // Small delay to show completion
    }
  }, [isUploadComplete, uploadedFiles, totalFiles, currentStep]);

  // Step 2: Message Generation Timer Effect (Decreasing)
  // Reason for Effect: Handle message generation countdown
  // Task Performed: Counts down from 5 seconds then triggers completion
  // Linking Information: Internal - Calls onComplete when finished
  useEffect(() => {
    if (!isVisible || currentStep !== 'message') return;

    const timer = setInterval(() => {
      setMessageCountdown(prev => {
        const newCount = prev - 1;
        if (newCount <= 0) {
          clearInterval(timer);
          setTimeout(() => {
            onComplete();
          }, 500);
          return 0;
        }
        return newCount;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [isVisible, currentStep, onComplete]);

  // Reset Effect
  // Reason for Effect: Reset component state when hidden
  // Task Performed: Resets all timers and states when component is hidden
  // Linking Information: Internal - Ensures clean state for next use
  useEffect(() => {
    if (!isVisible) {
      setCurrentStep('upload');
      setUploadTimer(0);
      setMessageCountdown(5);
      setUploadProgress(0);
    }
  }, [isVisible]);

  // Format Time Helper
  // Reason for Function: Format seconds into MM:SS format
  // Task Performed: Converts seconds to readable time format
  // Linking Information: Internal - Used for displaying upload timer
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-card border border-service-border rounded-2xl p-8 max-w-md w-full text-center">
        {/* Header */}
        <div className="mb-6">
          <h3 className="text-2xl font-bold text-foreground mb-2">
            {currentStep === 'upload' ? 'Uploading Your Files' : 'Preparing WhatsApp Message'}
          </h3>
          <p className="text-muted-foreground">
            {currentStep === 'upload' 
              ? 'Please wait while your files are being uploaded securely...'
              : 'Generating your complete order message with file links...'
            }
          </p>
        </div>

        {/* Step Indicator */}
        <div className="flex items-center justify-center mb-6 space-x-4">
          <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg ${
            currentStep === 'upload' ? 'bg-accent/20 text-accent' : 'bg-green-500/20 text-green-500'
          }`}>
            <Upload className="h-4 w-4" />
            <span className="text-sm font-medium">Step 1: Upload</span>
          </div>
          <div className="w-8 h-0.5 bg-muted"></div>
          <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg ${
            currentStep === 'message' ? 'bg-accent/20 text-accent' : 'bg-muted/20 text-muted-foreground'
          }`}>
            <MessageCircle className="h-4 w-4" />
            <span className="text-sm font-medium">Step 2: Message</span>
          </div>
        </div>

        {/* Timer Display */}
        <div className="relative w-32 h-32 mx-auto mb-6">
          <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 120 120">
            {/* Background circle */}
            <circle
              cx="60"
              cy="60"
              r="50"
              stroke="currentColor"
              strokeWidth="8"
              fill="none"
              className="text-muted/20"
            />
            {/* Progress circle */}
            <circle
              cx="60"
              cy="60"
              r="50"
              stroke="currentColor"
              strokeWidth="8"
              fill="none"
              strokeDasharray={`${2 * Math.PI * 50}`}
              strokeDashoffset={currentStep === 'upload' 
                ? `${2 * Math.PI * 50 * (1 - uploadProgress / 100)}`
                : `${2 * Math.PI * 50 * (messageCountdown / 5)}`
              }
              className="text-accent transition-all duration-1000 ease-out"
              strokeLinecap="round"
            />
          </svg>
          
          {/* Center content */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              {currentStep === 'upload' ? (
                <>
                  <Clock className="h-6 w-6 text-accent mx-auto mb-1" />
                  <div className="text-lg font-bold text-foreground">{formatTime(uploadTimer)}</div>
                  <div className="text-xs text-muted-foreground">Uploading...</div>
                </>
              ) : (
                <>
                  <MessageCircle className="h-6 w-6 text-accent mx-auto mb-1" />
                  <div className="text-2xl font-bold text-foreground">{messageCountdown}</div>
                  <div className="text-xs text-muted-foreground">Preparing...</div>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Progress Information */}
        {currentStep === 'upload' && (
          <div className="mb-6">
            <Progress value={uploadProgress} className="h-2 mb-2" />
            <p className="text-sm text-muted-foreground">
              {uploadedFiles} of {totalFiles} files uploaded ({Math.round(uploadProgress)}%)
            </p>
          </div>
        )}

        {/* Status Message */}
        <div className="p-4 bg-secondary/30 rounded-lg">
          <p className="text-sm text-muted-foreground">
            {currentStep === 'upload' 
              ? `Uploading ${totalFiles} file(s) securely to our servers. This may take a few minutes for larger files.`
              : 'Creating your WhatsApp message with all file links. WhatsApp will open automatically.'
            }
          </p>
        </div>
      </div>
    </div>
  );
};

export default TwoStepCountdown;
