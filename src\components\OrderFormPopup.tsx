import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { X, Upload, FileText, Image, Send, Trash2, Video, Music, Archive } from "lucide-react";
import { toast } from "sonner";
import { FileUploadService, AttachmentRecord } from "@/lib/supabase";
import TwoStepCountdown from "./TwoStepCountdown";

// Service Interface
// Reason for Interface: Define the structure of service data passed to the popup
// Task Performed: Type safety for service information
// Linking Information: Internal - Used by OrderFormPopup component for service details
interface Service {
  title: string;
  price: string;
  duration: string;
  description: string;
  features: string[];
}

// Duration Option Interface
// Reason for Interface: Define the structure of duration options with pricing
// Task Performed: Type safety for duration selection and pricing calculations
// Linking Information: Internal - Used by OrderFormPopup component for duration handling
interface DurationOption {
  seconds: number;
  label: string;
  price: number;
  originalPrice?: number;
  savings?: string;
  badge?: string;
}

// Form Data Interface
// Reason for Interface: Define the structure of form data collected from user
// Task Performed: Type safety for form validation and WhatsApp message generation
// Linking Information: Internal - Used by OrderFormPopup component for form handling
interface FormData {
  name: string;
  email: string;
  phone: string;
  requirements: string;
  attachments: File[];
  selectedDuration: number;
  videoCount: number;
  uploadedAttachments: AttachmentRecord[];
}

// Props Interface
// Reason for Interface: Define the props structure for the OrderFormPopup component
// Task Performed: Type safety for component props
// Linking Information: Internal - Used by parent components to pass data to OrderFormPopup
interface OrderFormPopupProps {
  isOpen: boolean;
  onClose: () => void;
  service: Service | null;
}

const OrderFormPopup = ({ isOpen, onClose, service }: OrderFormPopupProps) => {
  // Strategic Pricing Configuration
  // Reason for Configuration: Psychological pricing to make 60s package most attractive
  // Task Performed: Defines pricing structure that encourages 60s selection
  // Linking Information: Internal - Used by duration selection and price calculation
  const durationOptions: DurationOption[] = [
    {
      seconds: 15,
      label: '15 seconds',
      price: 9.99,
      badge: 'Basic'
    },
    {
      seconds: 30,
      label: '30 seconds',
      price: 19.99,
      originalPrice: 19.99,
      badge: 'Standard'
    },
    {
      seconds: 45,
      label: '45 seconds',
      price: 22.99,
      originalPrice: 29.97,
      savings: 'Save $6.98',
      badge: 'Popular'
    },
    {
      seconds: 60,
      label: '60 seconds',
      price: 24.99,
      originalPrice: 39.96,
      savings: 'Save $14.97',
      badge: 'BEST VALUE'
    }
  ];

  // Form State Management
  // Reason for State: Track user input data for form submission
  // Task Performed: Manages form data and validation states
  // Linking Information: Internal - Used throughout the component for form handling
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    phone: '',
    requirements: '',
    attachments: [],
    selectedDuration: 60, // Default to 60s (best value)
    videoCount: 0, // Default to 0 videos (user can specify)
    uploadedAttachments: []
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Partial<FormData>>({});
  const [showLoadingCountdown, setShowLoadingCountdown] = useState(false);
  const [uploadProgress, setUploadProgress] = useState({ uploaded: 0, total: 0 });
  const [totalFileSize, setTotalFileSize] = useState(0);
  const [isUploadComplete, setIsUploadComplete] = useState(false);

  // Price Calculation Function
  // Reason for Function: Calculate price based on selected duration
  // Task Performed: Returns pricing details for selected duration
  // Linking Information: Internal - Used by UI components to display current pricing
  const getSelectedDurationDetails = (): DurationOption => {
    return durationOptions.find(option => option.seconds === formData.selectedDuration) || durationOptions[3];
  };

  // Duration Change Handler
  // Reason for Function: Handle duration selection changes
  // Task Performed: Updates selected duration in form state
  // Linking Information: Internal - Used by duration selection radio buttons
  const handleDurationChange = (duration: number) => {
    setFormData(prev => ({ ...prev, selectedDuration: duration }));
  };

  // File Upload Handler
  // Reason for Function: Handle file upload functionality for attachments
  // Task Performed: Processes file selection and updates form state with size calculation
  // Linking Information: Internal - Used by file input onChange event
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      const newFiles = Array.from(files);

      // Calculate total file size
      const newTotalSize = newFiles.reduce((total, file) => total + file.size, 0);
      const currentTotalSize = formData.attachments.reduce((total, file) => total + file.size, 0);

      setFormData(prev => ({
        ...prev,
        attachments: [...prev.attachments, ...newFiles]
      }));

      setTotalFileSize(currentTotalSize + newTotalSize);
    }
  };

  // File Size Formatter
  // Reason for Function: Format file size in bytes to human readable format
  // Task Performed: Converts bytes to KB, MB, GB with appropriate units
  // Linking Information: Internal - Used for displaying file sizes in UI
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Remove File Handler
  // Reason for Function: Allow users to remove uploaded files
  // Task Performed: Removes specific file from attachments array and updates total size
  // Linking Information: Internal - Used by file removal buttons
  const removeFile = (index: number) => {
    const fileToRemove = formData.attachments[index];
    setFormData(prev => ({
      ...prev,
      attachments: prev.attachments.filter((_, i) => i !== index)
    }));

    // Update total file size
    setTotalFileSize(prev => prev - fileToRemove.size);
  };

  // Form Validation
  // Reason for Function: Validate form data before submission
  // Task Performed: Checks required fields and email format
  // Linking Information: Internal - Used by form submission handler
  const validateForm = (): boolean => {
    const newErrors: Partial<FormData> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    }

    if (!formData.requirements.trim()) {
      newErrors.requirements = 'Please describe your requirements';
    }

    if (!formData.selectedDuration || !durationOptions.some(option => option.seconds === formData.selectedDuration)) {
      newErrors.selectedDuration = 'Please select a duration';
    }

    if (formData.videoCount < 0) {
      newErrors.videoCount = 'Video count cannot be negative';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // WhatsApp Message Generator
  // Reason for Function: Format form data and service details into WhatsApp message with file URLs
  // Task Performed: Creates structured message with all order details including uploaded file URLs
  // Linking Information: Internal - Used by form submission to generate WhatsApp URL, External - Includes Supabase file URLs
  const generateWhatsAppMessage = (): string => {
    if (!service) return '';

    console.log('Generating WhatsApp message with form data:', formData);
    console.log('Uploaded attachments for message:', formData.uploadedAttachments);

    const selectedDuration = getSelectedDurationDetails();
    const savingsText = selectedDuration.savings ? ` (${selectedDuration.savings})` : '';
    const totalPrice = formData.videoCount > 0 ? (selectedDuration.price * formData.videoCount).toFixed(2) : '0.00';

    // Generate attachment section with URLs
    // Use temporary storage if form data doesn't have attachments yet
    const attachmentsToUse = formData.uploadedAttachments && formData.uploadedAttachments.length > 0
      ? formData.uploadedAttachments
      : (window as any).tempUploadedAttachments || [];

    let attachmentSection = '';
    if (attachmentsToUse && attachmentsToUse.length > 0) {
      attachmentSection = `📎 *Attachments:*
${attachmentsToUse.map((attachment: AttachmentRecord, index: number) =>
  `${index + 1}. ${attachment.file_name}
   📁 Size: ${(attachment.file_size / 1024 / 1024).toFixed(2)} MB
   🔗 Link: ${attachment.file_url}`
).join('\n\n')}`;
    } else {
      attachmentSection = '📎 *Attachments:* No attachments';
    }

    const videoSection = formData.videoCount > 0
      ? `🎥 *Video Count:* ${formData.videoCount}
💰 *Unit Price:* $${selectedDuration.price}${savingsText}
💰 *Total Price:* $${totalPrice}
🏷️ *Package:* ${selectedDuration.badge}`
      : `🎥 *Video Count:* 0 (Consultation/Custom Service)
💰 *Service Type:* Custom consultation
🏷️ *Package:* ${selectedDuration.badge}`;

    const message = `🎬 *NEW ORDER REQUEST*

📋 *Service Selected:* ${service.title}
⏱️ *Duration:* ${selectedDuration.label}
${videoSection}

👤 *Customer Details:*
• Name: ${formData.name}
• Email: ${formData.email}
• Phone: ${formData.phone}

📝 *Requirements:*
${formData.requirements}

✨ *Service Features:*
${service.features.map(feature => `• ${feature}`).join('\n')}

${attachmentSection}

---
*This order was placed through the website.*
*Files are securely stored and accessible via the provided links.*`;

    console.log('Generated WhatsApp message:', message);
    return encodeURIComponent(message);
  };

  // Form Submission Handler
  // Reason for Function: Handle form submission with file upload and redirect to WhatsApp
  // Task Performed: Validates form, uploads files, generates message with URLs, and opens WhatsApp
  // Linking Information: Internal - Used by Place Order button onClick event, External - Supabase for file storage
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setShowLoadingCountdown(true);
    setUploadProgress({ uploaded: 0, total: formData.attachments.length });
    setIsUploadComplete(false);

    try {
      // Upload files to Supabase if any attachments exist
      const uploadedAttachments: AttachmentRecord[] = [];

      if (formData.attachments.length > 0) {
        const totalSize = formData.attachments.reduce((sum, file) => sum + file.size, 0);
        const totalSizeMB = (totalSize / 1024 / 1024).toFixed(2);

        toast.info(`Starting upload of ${formData.attachments.length} file(s) (${totalSizeMB} MB total)...`);
        console.log(`Upload started: ${formData.attachments.length} files, ${totalSizeMB} MB total`);

        for (let i = 0; i < formData.attachments.length; i++) {
          const file = formData.attachments[i];
          const fileSizeMB = (file.size / 1024 / 1024).toFixed(2);

          try {
            console.log(`Uploading file ${i + 1}/${formData.attachments.length}: ${file.name} (${fileSizeMB} MB)`);
            toast.info(`Uploading: ${file.name} (${fileSizeMB} MB)`);

            const uploadedFile = await FileUploadService.uploadAttachment(file, formData.name);
            uploadedAttachments.push(uploadedFile);
            setUploadProgress({ uploaded: i + 1, total: formData.attachments.length });

            toast.success(`✅ Uploaded: ${file.name}`);
            console.log(`Successfully uploaded file ${i + 1}:`, uploadedFile);
          } catch (error) {
            console.error(`Failed to upload ${file.name}:`, error);
            toast.error(`❌ Failed to upload: ${file.name} - ${error instanceof Error ? error.message : 'Unknown error'}`);

            // Continue with other files even if one fails
            setUploadProgress({ uploaded: i + 1, total: formData.attachments.length });
          }
        }

        console.log(`Upload process completed. Successfully uploaded ${uploadedAttachments.length}/${formData.attachments.length} files:`, uploadedAttachments);

        if (uploadedAttachments.length > 0) {
          toast.success(`🎉 Upload complete! ${uploadedAttachments.length} file(s) uploaded successfully.`);
        }
      }

      // Update form data with uploaded attachments
      setFormData(prev => {
        const updatedData = { ...prev, uploadedAttachments };
        console.log('Updated form data with attachments:', updatedData);
        return updatedData;
      });

      // Store uploaded attachments in a ref or state that can be accessed by countdown complete
      // This ensures the attachments are available when generating the WhatsApp message
      window.tempUploadedAttachments = uploadedAttachments;

      // Mark upload as complete
      setIsUploadComplete(true);

      // Wait for countdown to complete before proceeding
      // The countdown component will call onComplete when ready

    } catch (error) {
      console.error('Error submitting form:', error);
      toast.error('Failed to process your order. Please try again.');
      setIsSubmitting(false);
      setShowLoadingCountdown(false);
    }
  };

  // Handle countdown completion and WhatsApp redirect
  // Reason for Function: Complete order process after file upload and countdown
  // Task Performed: Generates WhatsApp message with file URLs and opens WhatsApp
  // Linking Information: Internal - Called by LoadingCountdown component when timer completes
  const handleCountdownComplete = () => {
    try {
      // Generate WhatsApp message with uploaded file URLs
      const message = generateWhatsAppMessage();
      const whatsappNumber = '94777164818';
      const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${message}`;

      // Show success message first
      toast.success('Order processed successfully! Opening WhatsApp...');

      // Open WhatsApp in new tab with a slight delay to ensure it opens
      setTimeout(() => {
        const whatsappWindow = window.open(whatsappUrl, '_blank');

        // Check if popup was blocked
        if (!whatsappWindow || whatsappWindow.closed || typeof whatsappWindow.closed == 'undefined') {
          toast.error('Popup blocked! Please allow popups and try again.');
          // Fallback: try to navigate in same window
          window.location.href = whatsappUrl;
        } else {
          toast.success('WhatsApp opened successfully!');
        }
      }, 500);

      // Close popup after WhatsApp opens with longer delay
      setTimeout(() => {
        setShowLoadingCountdown(false);
        setIsSubmitting(false);

        // Reset form and close popup
        setTimeout(() => {
          // Clean up temporary storage
          delete (window as any).tempUploadedAttachments;

          onClose();
          setFormData({
            name: '',
            email: '',
            phone: '',
            requirements: '',
            attachments: [],
            selectedDuration: 60,
            videoCount: 0,
            uploadedAttachments: []
          });
          setErrors({});
          setUploadProgress({ uploaded: 0, total: 0 });
        }, 500);
      }, 2000);

    } catch (error) {
      console.error('Error completing order:', error);
      toast.error('Failed to complete your order. Please try again.');
      setIsSubmitting(false);
      setShowLoadingCountdown(false);
    }
  };

  // Input Change Handler
  // Reason for Function: Handle form input changes
  // Task Performed: Updates form state and clears related errors
  // Linking Information: Internal - Used by form inputs onChange events
  const handleInputChange = (field: keyof FormData, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  if (!isOpen || !service) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-card border border-service-border rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Fixed Header */}
        {/* Reason for Header: Form header with logo, title, and close button */}
        {/* Task Performed: Display order form header with company logo in right corner */}
        {/* Linking Information: Internal - Uses logo from public/images/logo folder */}
        <div className="bg-card border-b border-service-border p-6 rounded-t-2xl flex-shrink-0 z-10">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-foreground">Place Your Order</h2>
              <Badge className="bg-hero-gradient text-white border-0 mt-2">
                {service.title}
              </Badge>
            </div>
            <div className="flex items-center gap-3">
              {/* Company Logo */}
              <div className="w-12 h-12 bg-white rounded-lg shadow-md flex items-center justify-center">
                <img
                  src="/images/logo/Black & White Letter Z Logo.png"
                  alt="Company Logo"
                  className="w-8 h-8 object-contain"
                />
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="text-muted-foreground hover:text-foreground"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>

        {/* Scrollable Form Content */}
        <div className="flex-1 overflow-y-auto">

        {/* Form Content */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6 pb-8">
          {/* Service Summary */}
          <div className="bg-secondary/50 rounded-lg p-4 border border-service-border">
            <h3 className="font-semibold text-foreground mb-4">Service: {service.title}</h3>
            <div className="bg-card rounded-lg p-4 border border-accent/20">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-lg font-semibold text-foreground">
                    Unit Price: ${getSelectedDurationDetails().price}
                  </p>
                  <p className="text-2xl font-bold text-accent">
                    Total: ${(getSelectedDurationDetails().price * formData.videoCount).toFixed(2)}
                  </p>
                  {getSelectedDurationDetails().originalPrice && (
                    <p className="text-sm text-muted-foreground line-through">
                      Original: ${getSelectedDurationDetails().originalPrice}
                    </p>
                  )}
                  <p className="text-sm text-muted-foreground">
                    Videos: {formData.videoCount}
                  </p>
                </div>
                <div className="text-right">
                  <Badge className="bg-hero-gradient text-white border-0 mb-1">
                    {getSelectedDurationDetails().badge}
                  </Badge>
                  {getSelectedDurationDetails().savings && (
                    <p className="text-sm text-green-400 font-semibold">
                      {getSelectedDurationDetails().savings}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Duration Selection */}
          <div className="space-y-4 pt-2">
            <Label className="text-foreground font-medium text-lg">
              Choose Video Duration *
            </Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pb-4">
              {durationOptions.map((option) => (
                <div
                  key={option.seconds}
                  className={`relative border-2 rounded-lg p-4 cursor-pointer transition-all duration-300 ${
                    formData.selectedDuration === option.seconds
                      ? 'border-accent bg-accent/10'
                      : 'border-service-border hover:border-accent/50'
                  }`}
                  onClick={() => handleDurationChange(option.seconds)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <input
                        type="radio"
                        name="duration"
                        value={option.seconds}
                        checked={formData.selectedDuration === option.seconds}
                        onChange={() => handleDurationChange(option.seconds)}
                        className="w-4 h-4 text-accent"
                      />
                      <div>
                        <p className="font-semibold text-foreground">{option.label}</p>
                        <p className="text-sm text-muted-foreground">{option.badge}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-lg font-bold text-accent">${option.price}</p>
                      {option.originalPrice && option.originalPrice !== option.price && (
                        <p className="text-sm text-muted-foreground line-through">
                          ${option.originalPrice}
                        </p>
                      )}
                      {option.savings && (
                        <p className="text-xs text-green-400 font-semibold">
                          {option.savings}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Best Value Badge */}
                  {option.badge === 'BEST VALUE' && (
                    <div className="absolute -top-2 -right-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                      BEST VALUE
                    </div>
                  )}

                  {/* Popular Badge */}
                  {option.badge === 'Popular' && (
                    <div className="absolute -top-2 -right-2 bg-gradient-to-r from-orange-500 to-red-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                      POPULAR
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Video Count Field */}
          <div className="space-y-2">
            <Label htmlFor="videoCount" className="text-foreground font-medium">
              Number of Videos *
            </Label>
            <Input
              id="videoCount"
              type="number"
              min="1"
              max="50"
              value={formData.videoCount}
              onChange={(e) => {
                const value = parseInt(e.target.value) || 1;
                handleInputChange('videoCount', value);
              }}
              placeholder="Enter number of videos"
              className={`bg-background border-service-border ${errors.videoCount ? 'border-red-500' : ''}`}
            />
            {errors.videoCount && (
              <p className="text-red-500 text-sm">{errors.videoCount}</p>
            )}
            <p className="text-xs text-muted-foreground">
              Specify how many videos you want (1-50)
            </p>
          </div>

          {/* Name Field */}
          <div className="space-y-2">
            <Label htmlFor="name" className="text-foreground font-medium">
              Full Name *
            </Label>
            <Input
              id="name"
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="Enter your full name"
              className={`bg-background border-service-border ${errors.name ? 'border-red-500' : ''}`}
            />
            {errors.name && (
              <p className="text-red-500 text-sm">{errors.name}</p>
            )}
          </div>

          {/* Email Field */}
          <div className="space-y-2">
            <Label htmlFor="email" className="text-foreground font-medium">
              Email Address *
            </Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              placeholder="Enter your email address"
              className={`bg-background border-service-border ${errors.email ? 'border-red-500' : ''}`}
            />
            {errors.email && (
              <p className="text-red-500 text-sm">{errors.email}</p>
            )}
          </div>

          {/* Phone Number Field */}
          <div className="space-y-2">
            <Label htmlFor="phone" className="text-foreground font-medium">
              Phone Number *
            </Label>
            <Input
              id="phone"
              type="tel"
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              placeholder="Enter your phone number"
              className={`bg-background border-service-border ${errors.phone ? 'border-red-500' : ''}`}
            />
            {errors.phone && (
              <p className="text-red-500 text-sm">{errors.phone}</p>
            )}
          </div>

          {/* Requirements Field */}
          <div className="space-y-2">
            <Label htmlFor="requirements" className="text-foreground font-medium">
              Project Requirements *
            </Label>
            <Textarea
              id="requirements"
              value={formData.requirements}
              onChange={(e) => handleInputChange('requirements', e.target.value)}
              placeholder="Describe your project requirements, style preferences, target audience, etc."
              rows={4}
              className={`bg-background border-service-border resize-none ${errors.requirements ? 'border-red-500' : ''}`}
            />
            {errors.requirements && (
              <p className="text-red-500 text-sm">{errors.requirements}</p>
            )}
          </div>

          {/* File Upload */}
          <div className="space-y-2">
            <Label className="text-foreground font-medium">
              Attachments (Optional)
            </Label>
            <div className="border-2 border-dashed border-service-border rounded-lg p-4 text-center">
              <input
                type="file"
                multiple
                onChange={handleFileUpload}
                className="hidden"
                id="file-upload"
                accept="*/*"
              />
              <label
                htmlFor="file-upload"
                className="cursor-pointer flex flex-col items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"
              >
                <Upload className="h-8 w-8" />
                <span>Click to upload files</span>
                <span className="text-xs">Any file type supported (Images, Videos, PDFs, Documents)</span>
              </label>
            </div>

            {/* Uploaded Files */}
            {formData.attachments.length > 0 && (
              <div className="space-y-2">
                <p className="text-sm font-medium text-foreground">Uploaded Files:</p>
                {formData.attachments.map((file, index) => (
                  <div key={index} className="flex items-center justify-between bg-secondary/50 rounded-lg p-3">
                    <div className="flex items-center gap-2">
                      {file.type.startsWith('image/') ? (
                        <Image className="h-4 w-4 text-blue-500" />
                      ) : file.type.startsWith('video/') ? (
                        <Video className="h-4 w-4 text-purple-500" />
                      ) : file.type.startsWith('audio/') ? (
                        <Music className="h-4 w-4 text-orange-500" />
                      ) : file.type.includes('zip') || file.type.includes('rar') || file.type.includes('archive') ? (
                        <Archive className="h-4 w-4 text-yellow-500" />
                      ) : (
                        <FileText className="h-4 w-4 text-green-500" />
                      )}
                      <span className="text-sm text-foreground">{file.name}</span>
                      <span className="text-xs text-muted-foreground">
                        ({formatFileSize(file.size)})
                      </span>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFile(index)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Submit Button */}
          <div className="pt-4 border-t border-service-border">
            <Button
              type="submit"
              disabled={isSubmitting}
              className="w-full bg-hero-gradient hover:opacity-90 transition-opacity py-6 text-lg font-semibold"
            >
              {isSubmitting ? (
                'Processing...'
              ) : (
                <>
                  <Send className="mr-2 h-5 w-5" />
                  Place Order via WhatsApp
                </>
              )}
            </Button>
            <p className="text-xs text-muted-foreground text-center mt-2">
              You'll be redirected to WhatsApp with your order details
            </p>
          </div>

          {/* Secure Payments Section */}
          {/* Reason for Section: Display accepted payment methods for customer confidence */}
          {/* Task Performed: Shows PayPal and Payoneer payment options with icons */}
          {/* Linking Information: Internal - Added below form submission area as requested */}
          <div className="mt-6 pt-6 border-t border-service-border">
            <h3 className="text-lg font-semibold text-foreground text-center mb-4">
              Secure Payments
            </h3>
            <div className="flex justify-center items-center gap-8">
              {/* PayPal */}
              <div className="group cursor-pointer">
                <div className="w-16 h-16 bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300 flex items-center justify-center group-hover:scale-105">
                  <img
                    src="https://cdn.worldvectorlogo.com/logos/paypal-2.svg"
                    alt="PayPal Payment"
                    className="w-12 h-12 object-contain"
                  />
                </div>
                <p className="text-center mt-2 text-xs font-medium text-muted-foreground group-hover:text-foreground transition-colors">
                  PayPal
                </p>
              </div>

              {/* Payoneer */}
              <div className="group cursor-pointer">
                <div className="w-16 h-16 bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300 flex items-center justify-center group-hover:scale-105">
                  <img
                    src="https://cdn.worldvectorlogo.com/logos/payoneer-2.svg"
                    alt="Payoneer Payment"
                    className="w-12 h-12 object-contain"
                  />
                </div>
                <p className="text-center mt-2 text-xs font-medium text-muted-foreground group-hover:text-foreground transition-colors">
                  Payoneer
                </p>
              </div>
            </div>
            <p className="text-xs text-muted-foreground text-center mt-4">
              All payments are processed securely through trusted payment providers
            </p>
          </div>
        </form>
        </div>
      </div>

      {/* Two-Step Countdown Overlay */}
      <TwoStepCountdown
        isVisible={showLoadingCountdown}
        onComplete={handleCountdownComplete}
        totalFiles={formData.attachments.length}
        uploadedFiles={uploadProgress.uploaded}
        isUploadComplete={isUploadComplete}
      />
    </div>
  );
};

export default OrderFormPopup;
