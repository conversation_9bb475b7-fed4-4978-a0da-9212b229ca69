import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>ir<PERSON>, <PERSON>u, X } from "lucide-react";
import { useState } from "react";

// WhatsApp SVG Icon Component
// Reason for Function: Create a custom WhatsApp icon since lucide-react doesn't have one
// Task Performed: Renders the official WhatsApp icon as an SVG component
// Linking Information: Internal - Used in Header component for WhatsApp contact button
const WhatsAppIcon = ({ size = 18, className = "" }: { size?: number; className?: string }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="currentColor"
    className={className}
  >
    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.386"/>
  </svg>
);

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
      setIsMenuOpen(false);
    }
  };

  // WhatsApp Contact Handler
  // Reason for Function: Handle WhatsApp contact button clicks
  // Task Performed: Opens WhatsApp chat in new tab with predefined number
  // Linking Information: Internal - Used by WhatsApp contact buttons in Header component
  const handleWhatsAppClick = () => {
    // WhatsApp number: +94777164818 (without + sign for URL)
    const whatsappNumber = '94777164818';
    window.open(`https://wa.me/${whatsappNumber}`, '_blank');
  };

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-background/80 backdrop-blur-lg border-b border-border">
      <div className="container mx-auto px-4 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          {/* Reason for Styling: Use Oswald SemiBold 600 with consistent text size for brand title */}
          {/* Task Performed: Apply Oswald font family with semibold weight and uniform sizing */}
          {/* Linking Information: Internal - Main brand title displayed in header navigation */}
          <div className="text-3xl font-oswald font-semibold bg-hero-gradient bg-clip-text text-transparent tracking-tight">
            Reel For You.Com
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <button
              onClick={() => scrollToSection('hero')}
              className="text-foreground hover:text-accent transition-colors"
            >
              Home
            </button>
            <button
              onClick={() => scrollToSection('why-choose-us')}
              className="text-foreground hover:text-accent transition-colors"
            >
              Why Us
            </button>
            <button
              onClick={() => scrollToSection('projects')}
              className="text-foreground hover:text-accent transition-colors"
            >
              Projects
            </button>
            <button
              onClick={() => scrollToSection('our-team')}
              className="text-foreground hover:text-accent transition-colors"
            >
              Our Team
            </button>
            <button
              onClick={() => scrollToSection('services')}
              className="text-foreground hover:text-accent transition-colors"
            >
              Services
            </button>
            <button
              onClick={() => scrollToSection('reviews')}
              className="text-foreground hover:text-accent transition-colors"
            >
              Reviews
            </button>
          </nav>

          {/* WhatsApp Contact Button */}
          <Button
            onClick={handleWhatsAppClick}
            className="hidden md:flex items-center gap-2 bg-hero-gradient hover:opacity-90 transition-opacity"
          >
            <WhatsAppIcon size={18} />
            WhatsApp
          </Button>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="md:hidden p-2"
          >
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-border">
            <nav className="flex flex-col space-y-4">
              <button
                onClick={() => scrollToSection('hero')}
                className="text-left text-foreground hover:text-accent transition-colors"
              >
                Home
              </button>
              <button
                onClick={() => scrollToSection('why-choose-us')}
                className="text-left text-foreground hover:text-accent transition-colors"
              >
                Why Us
              </button>
              <button
                onClick={() => scrollToSection('projects')}
                className="text-left text-foreground hover:text-accent transition-colors"
              >
                Projects
              </button>
              <button
                onClick={() => scrollToSection('our-team')}
                className="text-left text-foreground hover:text-accent transition-colors"
              >
                Our Team
              </button>
              <button
                onClick={() => scrollToSection('services')}
                className="text-left text-foreground hover:text-accent transition-colors"
              >
                Services
              </button>
              <button
                onClick={() => scrollToSection('reviews')}
                className="text-left text-foreground hover:text-accent transition-colors"
              >
                Reviews
              </button>
              <Button
                onClick={handleWhatsAppClick}
                className="self-start mt-4 bg-hero-gradient hover:opacity-90 transition-opacity"
              >
                <WhatsAppIcon size={18} className="mr-2" />
                WhatsApp
              </Button>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;