// Supabase Configuration
// Reason for Configuration: Centralized Supabase client setup for database operations
// Task Performed: Creates and exports Supabase client instance for file storage and database operations
// Linking Information: Internal - Used throughout the application for database and storage operations

import { createClient } from '@supabase/supabase-js';

// Supabase project configuration
const supabaseUrl = 'https://mvfmyzhxynhgspfmonox.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im12Zm15emh4eW5oZ3NwZm1vbm94Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM1MTQ2ODgsImV4cCI6MjA2OTA5MDY4OH0.0webiBBGGp_GMhS4wOmwqGSIZ2YcowP6mrpnqitckR4';

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Database Types
// Reason for Types: Type safety for database operations and form data
// Task Performed: Defines TypeScript interfaces for database tables and operations
// Linking Information: Internal - Used by components for type-safe database operations

export interface AttachmentRecord {
  id?: number;
  client_name: string;
  file_name: string;
  file_size: number;
  file_type: string;
  file_url: string;
  storage_path: string;
  uploaded_at?: string;
  created_at?: string;
  updated_at?: string;
}

// Video Record Interface
// Reason for Interface: Type safety for video database operations
// Task Performed: Defines TypeScript interface for samples_video table
// Linking Information: Internal - Used by VideoService for type-safe video operations
export interface SampleVideoRecord {
  id?: number;
  file_name: string;
  title?: string;
  description?: string;
  category?: string;
  platform?: string;
  duration?: string;
  views?: string;
  engagement?: string;
  file_url: string;
  storage_path: string;
  file_size?: number;
  file_type?: string;
  thumbnail_url?: string;
  is_hero_video?: boolean;
  is_active?: boolean;
  uploaded_at?: string;
  created_at?: string;
  updated_at?: string;
}

// File Upload Service
// Reason for Service: Centralized file upload functionality with Supabase Storage
// Task Performed: Handles file uploads to Supabase Storage and database record creation
// Linking Information: Internal - Used by OrderFormPopup component for file upload operations

export class FileUploadService {
  
  // Upload File to Supabase Storage with Progress Tracking
  // Reason for Function: Upload files to Supabase Storage bucket with better error handling
  // Task Performed: Uploads file to storage and returns public URL with retry logic
  // Linking Information: Internal - Used by uploadAttachment method
  static async uploadFileToStorage(file: File, clientName: string): Promise<string> {
    const fileExt = file.name.split('.').pop();
    const fileName = `${clientName.replace(/[^a-zA-Z0-9]/g, '_')}_${Date.now()}.${fileExt}`;
    const filePath = `attachments/${fileName}`;

    console.log(`Starting upload of ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`);

    try {
      const { data, error } = await supabase.storage
        .from('attachments')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (error) {
        console.error('Upload error:', error);
        throw new Error(`File upload failed: ${error.message}`);
      }

      console.log(`Upload successful for ${file.name}`);

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('attachments')
        .getPublicUrl(filePath);

      console.log(`Public URL generated: ${publicUrl}`);
      return publicUrl;
    } catch (error) {
      console.error(`Failed to upload ${file.name}:`, error);
      throw error;
    }
  }

  // Upload Attachment with Database Record
  // Reason for Function: Complete file upload process with database record creation
  // Task Performed: Uploads file to storage and creates database record with better error handling
  // Linking Information: Internal - Used by OrderFormPopup component for complete file upload
  static async uploadAttachment(file: File, clientName: string): Promise<AttachmentRecord> {
    try {
      console.log(`Starting complete upload process for ${file.name}`);

      // Upload file to storage
      const fileUrl = await this.uploadFileToStorage(file, clientName);

      // Create database record
      const cleanClientName = clientName.replace(/[^a-zA-Z0-9]/g, '_');
      const attachmentData: Omit<AttachmentRecord, 'id' | 'uploaded_at' | 'created_at' | 'updated_at'> = {
        client_name: clientName,
        file_name: file.name,
        file_size: file.size,
        file_type: file.type || 'application/octet-stream',
        file_url: fileUrl,
        storage_path: `attachments/${cleanClientName}_${Date.now()}.${file.name.split('.').pop()}`
      };

      console.log('Creating database record:', attachmentData);

      const { data, error } = await supabase
        .from('attachments')
        .insert([attachmentData])
        .select()
        .single();

      if (error) {
        console.error('Database insert error:', error);
        throw new Error(`Database insert failed: ${error.message}`);
      }

      console.log(`Complete upload successful for ${file.name}:`, data);
      return data as AttachmentRecord;
    } catch (error) {
      console.error(`Upload attachment error for ${file.name}:`, error);
      throw error;
    }
  }

  // Get Attachments by Client Name
  // Reason for Function: Retrieve all attachments for a specific client
  // Task Performed: Queries database for client attachments
  // Linking Information: Internal - Used for retrieving client attachment URLs
  static async getAttachmentsByClient(clientName: string): Promise<AttachmentRecord[]> {
    const { data, error } = await supabase
      .from('attachments')
      .select('*')
      .eq('client_name', clientName)
      .order('uploaded_at', { ascending: false });

    if (error) {
      throw new Error(`Failed to fetch attachments: ${error.message}`);
    }

    return data as AttachmentRecord[];
  }
}

// Video Service
// Reason for Service: Centralized video management functionality with Supabase
// Task Performed: Handles video retrieval from samples_video table and storage operations
// Linking Information: Internal - Used by Hero and RecentProjects components for video operations

export class VideoService {

  // Get Hero Video
  // Reason for Function: Retrieve the main introduction video for Hero section
  // Task Performed: Fetches hero video from samples_video table where is_hero_video is true
  // Linking Information: Internal - Used by Hero component to load intro video
  static async getHeroVideo(): Promise<SampleVideoRecord | null> {
    try {
      console.log('Fetching hero video from Supabase...');

      const { data, error } = await supabase
        .from('samples_video')
        .select('*')
        .eq('is_hero_video', true)
        .eq('is_active', true)
        .single();

      if (error) {
        console.error('Error fetching hero video:', error);
        throw new Error(`Failed to fetch hero video: ${error.message}`);
      }

      console.log('Hero video fetched successfully:', data);
      return data as SampleVideoRecord;
    } catch (error) {
      console.error('Hero video fetch error:', error);
      return null;
    }
  }

  // Get All Active Videos
  // Reason for Function: Retrieve all active videos for portfolio/success stories
  // Task Performed: Fetches all active videos from samples_video table
  // Linking Information: Internal - Used by RecentProjects component for video gallery
  static async getAllActiveVideos(): Promise<SampleVideoRecord[]> {
    try {
      console.log('Fetching all active videos from Supabase...');

      const { data, error } = await supabase
        .from('samples_video')
        .select('*')
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching videos:', error);
        throw new Error(`Failed to fetch videos: ${error.message}`);
      }

      console.log(`Fetched ${data?.length || 0} active videos`);
      return data as SampleVideoRecord[];
    } catch (error) {
      console.error('Videos fetch error:', error);
      return [];
    }
  }

  // Get Videos by Category
  // Reason for Function: Retrieve videos filtered by specific category
  // Task Performed: Fetches videos from samples_video table filtered by category
  // Linking Information: Internal - Used for categorized video displays
  static async getVideosByCategory(category: string): Promise<SampleVideoRecord[]> {
    try {
      console.log(`Fetching videos for category: ${category}`);

      const { data, error } = await supabase
        .from('samples_video')
        .select('*')
        .eq('category', category)
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching videos by category:', error);
        throw new Error(`Failed to fetch videos for category ${category}: ${error.message}`);
      }

      console.log(`Fetched ${data?.length || 0} videos for category: ${category}`);
      return data as SampleVideoRecord[];
    } catch (error) {
      console.error('Category videos fetch error:', error);
      return [];
    }
  }

  // Get Video by File Name
  // Reason for Function: Retrieve specific video by its file name
  // Task Performed: Fetches single video record by file_name
  // Linking Information: Internal - Used for specific video lookups
  static async getVideoByFileName(fileName: string): Promise<SampleVideoRecord | null> {
    try {
      console.log(`Fetching video by file name: ${fileName}`);

      const { data, error } = await supabase
        .from('samples_video')
        .select('*')
        .eq('file_name', fileName)
        .single();

      if (error) {
        console.error('Error fetching video by file name:', error);
        throw new Error(`Failed to fetch video ${fileName}: ${error.message}`);
      }

      console.log('Video fetched successfully:', data);
      return data as SampleVideoRecord;
    } catch (error) {
      console.error('Video fetch error:', error);
      return null;
    }
  }
}
