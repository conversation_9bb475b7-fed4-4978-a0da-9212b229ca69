# ReelVision Studio

![ReelVision Studio Logo](public/favicon.svg)

## Professional Video Creation & Editing Services

ReelVision Studio is a premium video creation and editing service specializing in short-form content for social media platforms. With over 5 years of experience and 500+ satisfied clients, we transform your ideas into engaging, viral-worthy content.

## Our Services

- **Instagram Reels** - Captivating vertical videos optimized for Instagram's algorithm
- **YouTube Shorts** - Engaging short-form content designed to maximize views and subscribers
- **TikTok Videos** - Trending content that captures attention and drives engagement
- **Pinterest Video Pins** - Visually stunning video pins that drive traffic and conversions

## Features

- **Professional Editing** - High-quality video editing with seamless transitions
- **Custom Graphics** - Branded overlays, animations, and text effects
- **Music Selection** - Trending audio and licensed music integration
- **Optimization** - Platform-specific formatting and optimization
- **Unlimited Revisions** - Refinements until you're completely satisfied

## Technologies Used

This website is built with modern web technologies:

- **React** - Frontend library for building user interfaces
- **TypeScript** - Type-safe JavaScript for better code quality
- **Tailwind CSS** - Utility-first CSS framework for rapid UI development
- **shadcn/ui** - High-quality UI components built with Radix UI and Tailwind
- **Vite** - Next-generation frontend tooling for faster development

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn

### Installation

1. Clone the repository
   ```sh
   git clone https://github.com/yourusername/reelvision-studio.git
   ```

2. Navigate to the project directory
   ```sh
   cd reelvision-studio
   ```

3. Install dependencies
   ```sh
   npm install
   # or
   yarn install
   ```

4. Start the development server
   ```sh
   npm run dev
   # or
   yarn dev
   ```

5. Open your browser and visit `http://localhost:5173`

## Project Structure

```
reelvision-studio/
├── public/               # Static assets
│   ├── images/           # Image assets
│   │   └── reviews/      # Client review screenshots
│   └── favicon.svg       # Site favicon
├── src/                  # Source code
│   ├── assets/           # Project assets
│   ├── components/       # React components
│   │   ├── ui/           # UI components (buttons, cards, etc.)
│   │   └── ...           # Feature components
│   ├── pages/            # Page components
│   └── ...               # Other source files
└── ...                   # Configuration files
```

## Key Features of Our Website

- **Responsive Design** - Optimized for all devices from mobile to desktop
- **Modern UI/UX** - Premium look and feel with smooth animations
- **Video Showcase** - Embedded video player for our portfolio
- **Client Reviews** - Real testimonials from satisfied clients
- **WhatsApp Integration** - Direct contact through WhatsApp for inquiries
- **Order Management** - Advanced order form with file uploads and pricing options
- **Team Showcase** - Professional team member profiles and expertise

## Sections Overview

### 🏠 Hero Section
- Engaging video background with YouTube integration
- Clear value proposition and call-to-action
- WhatsApp contact integration

### ⭐ Why Choose Us
- Key differentiators and unique selling points
- Statistics and achievements showcase
- Premium visual design with animations

### 🎬 Our Latest Success Stories
- Portfolio of completed projects with real YouTube videos
- Embedded video player for seamless viewing
- Platform-specific categorization (Instagram, TikTok, Pinterest, YouTube)

### 👥 Our Team
- Professional team member profiles
- Skills and expertise showcase
- Years of experience and specializations

### 🛍️ Choose Your Video Platform
- Service packages with duration options (15s, 30s, 45s, 60s)
- Strategic pricing with psychological appeal
- Advanced order form with file upload capabilities

### ⭐ Client Reviews
- Animated carousel of real WhatsApp review screenshots
- Continuous sliding animation with direction changes
- Authentic client testimonials

### 📞 Contact & Call-to-Action
- Multiple contact methods
- WhatsApp integration throughout the site
- Professional inquiry handling

## Contact Us

Ready to create viral-worthy content? Get in touch with us:

- **WhatsApp**: +94 777 164 818
- **Email**: <EMAIL>
- **Website**: [www.reelsforyou.com](https://www.reelsforyou.com)

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

### Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## License

© 2025 ReelVision Studio. All Rights Reserved.

---

**Built with ❤️ by ReelVision Studio Team**
